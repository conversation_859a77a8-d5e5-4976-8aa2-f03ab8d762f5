import random
import mysql.connector
from profiler import Profiler
import shutil
import os
from PIL import Image

def load_template(file_name):
    with open(file_name, 'r') as file:
        template = file.read()
        file.close()
    return template

def shorten_content(content):
    # Remove HTML tags
    # content = re.sub('<[^<]+?>', '', content)
    content = content.replace('\n', ' ')
    content = content.replace('"', '&quot;')
    if len(content) > 150:
        # Find the last space before the 120th character
        last_space = content.rfind(' ', 0, 150)
        if last_space == -1:
            # If no space is found, truncate at 150 characters
            return content[:150] + " (...)"
        else:
            # Truncate at the last space
            return content[:last_space] + " (...)"
    return content

profiler = Profiler()
profiler.start("generate_htmls")

## TODO: add classification tags (human, vala, maia, hobbit, orc, dragon, river, lake, mountain, forest, kingdom, king, etc...)
## TODO: add multi-language support (en / de)
## TODO: add filter form on index page

images_directory = "images/"
images_formats   = ["webp", "jpg", "png"]
output_directory = "output_files/"
index_file_name  = "index.html"

# Load templates
entry_template     = load_template('entry_template.html')
index_template     = load_template('index_template.html')
item_template      = load_template('item_template.html')
category_template  = load_template('category_template.html')
paragraph_template = load_template('paragraph_template.html')
link_template      = load_template('link_template.html')
image_template     = load_template('image_template.html')

# Connect to database
try:
   cnx = mysql.connector.connect(user='app_user', password='1234',host='localhost',database='encyclopedia')
except mysql.connector.Error as error:
   print(error)
   exit(1)
else:
   cursor = cnx.cursor(dictionary=True)

   select_categories = "select id,short_description,description from category;"
   cursor.execute(select_categories)
   categories = cursor.fetchall()

   categories_html = ""
   categories_map = {}
   for category in categories:
      categories_map.__setitem__(category['id'],category)
      categories_html += category_template.replace("{{category_short_description}}", category['short_description']) \
                                          .replace("{{category_description}}",category['description'])

   select_entries = "select id,primary_title,tagged_content,title_tag,category_id,content from entry order by primary_title;"
   cursor.execute(select_entries)
   entries = cursor.fetchall()

   # clean htmls
   for file_name in os.listdir(output_directory):
      if file_name.endswith(".html"):
         profiler.start("clean_htmls")
         os.remove(output_directory + file_name)
         profiler.stop("clean_htmls")

   entries_dict = {}
   for entry in entries:
       entry_id = entry['id']
       primary_title = entry['primary_title']
       tagged_content = entry['tagged_content']
       title_tag = entry['title_tag']
       category_id = entry['category_id']
       content = entry['content']
       
       # Store the entry details in a dictionary with the primary title as the key
       entries_dict[title_tag] = {
           "id": entry_id,
           "content": content,
           "category_id": category_id
       }

   select_title = "select id, entry_id, flag_primary, flag_hidden, description, entry_tag from title"
   cursor.execute(select_title)
   titles = cursor.fetchall()

   items_html = ""

   for entry in entries:
      parsed_content = entry['tagged_content']
      parsed_html = entry_template.replace("{{main_title}}", entry['primary_title'])
      file_name = entry['title_tag'] + ".html"

      additional_titles = ""
      for title in titles:
         if((entry['id'] == title['entry_id']) & (title['flag_primary'] != 1) & (title['flag_hidden'] != 1)):
            additional_titles += title['description'] + ", "

         shortened_content = shorten_content(entries_dict[title['entry_tag']]["content"])
         link = link_template.replace("{{link}}", title['entry_tag'] + ".html").replace("{{name}}", title['description']).replace("{{shortened_content}}", shortened_content)
         parsed_content = parsed_content.replace("{{" + title['description'] + "}}", link)

      if(additional_titles != ""):
         additional_titles = additional_titles[0:len(additional_titles)-2]

      parsed_html = parsed_html.replace("{{additional_titles}}", additional_titles)

      parsed_html = parsed_html.replace("{{category_description}}", categories_map[entry['category_id']]['description']) \
                               .replace("{{category_short_description}}", categories_map[entry['category_id']]['short_description'])

      # check if image exists
      aspect_ratio = None
      for image_format in images_formats:
         image_file = images_directory + entry['title_tag'] + "." + image_format
         if exists := os.path.exists(image_file):
            width, height = Image.open(image_file).size
            aspect_ratio = "portrait"
            if(width > height):
                aspect_ratio = "landscape"
            parsed_html = parsed_html.replace("{{image}}", (image_template.replace("{{aspect_ratio}}", aspect_ratio).replace("{{image_file}}", image_file)))
            break
      
      if aspect_ratio is None:
         parsed_html = parsed_html.replace("{{image}}", "")

      content = ""
      paragraphs = parsed_content.split("\n")

      for paragraph in paragraphs:
         content += paragraph_template.replace("{{paragraph}}", paragraph)

      parsed_html = parsed_html.replace("{{content}}", content)

      profiler.start("replace_tags")
      items_html = items_html + item_template.replace("{{main_title}}", entry['primary_title']) \
                                             .replace("{{additional_titles}}", additional_titles) \
                                             .replace("{{link}}",entry['title_tag']+".html") \
                                             .replace("{{category_short_description}}", categories_map[entry['category_id']]['short_description']) \
                                             .replace("{{category_description}}", categories_map[entry['category_id']]['description']) \
                                             .replace("{{title_tag}}", entry['title_tag']) \
                                             .replace("{{random_number}}", str(random.randint(0, 9)).zfill(2))
      profiler.stop("replace_tags")

      profiler.start("write_files")
      with open(output_directory + file_name, 'w') as f:
         f.write(parsed_html)
         f.close()
         # DEBUG: print(file_name + "\t" + categories_map[entry['category_id']]['short_description'])
      profiler.stop("write_files")

   index_html = index_template.replace("{{categories}}", categories_html).replace("{{items}}", items_html)

   profiler.start("write_index")
   with open(output_directory + index_file_name, 'w') as f:
      f.write(index_html)
      f.close()
   profiler.stop("write_index")

   cnx.commit()
   cnx.close()

# Copy styles folder to output directory
profiler.start("copy_styles")
styles_source = os.path.join(os.path.dirname(__file__), 'styles')
styles_dest = os.path.join(output_directory, 'styles')

# Remove existing styles directory in output if it exists
if os.path.exists(styles_dest):
    shutil.rmtree(styles_dest)

# Copy the styles directory
shutil.copytree(styles_source, styles_dest)
profiler.stop("copy_styles")

# Copy images folder to output directory
profiler.start("copy_images")
images_source = os.path.join(os.path.dirname(__file__), images_directory)
images_dest = os.path.join(output_directory, images_directory)

# Remove existing styles directory in output if it exists
if os.path.exists(images_dest):
    shutil.rmtree(images_dest)

# Copy the styles directory
shutil.copytree(images_source, images_dest)
profiler.stop("copy_images")

profiler.stop("generate_htmls")
profiler.report()
