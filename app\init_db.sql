CREATE USER 'app_user'@'%' IDENTIFIED BY '1234';
CREATE DATABASE encyclopedia;
GRANT ALL PRIVILEGES ON encyclopedia.* TO 'app_user'@'%';

USE encyclopedia;

DROP TABLE IF EXISTS `title`;
DROP TABLE IF EXISTS `entry`;
DROP TABLE IF EXISTS `category`;

CREATE TABLE `category` (
  `id` int NOT NULL AUTO_INCREMENT,
  `short_description` varchar(5) NOT NULL,
  `description` varchar(40) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE `entry` (
  `id` int NOT NULL AUTO_INCREMENT,
  `category_id` int NOT NULL,
  `primary_title` varchar(40) NOT NULL,
  `title_tag` varchar(40) NOT NULL,
  `content` text NOT NULL,
  `tagged_content` text,
  PRIMARY KEY (`id`),
  CONSTRAINT `category_id` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`)  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- title: table
CREATE TABLE `title` (
  `id` int NOT NULL AUTO_INCREMENT,
  `flag_primary` tinyint NOT NULL DEFAULT '0',
  `flag_hidden` tinyint NOT NULL DEFAULT '0',
  `description` varchar(40) NOT NULL,
  `entry_tag` varchar(40) NOT NULL,
  `entry_id` int NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `entry_id` FOREIGN KEY (`entry_id`) REFERENCES `entry` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;