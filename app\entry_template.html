<!DOCTYPE html>
<html lang="pt-br">
 <head>
   <title>{{main_title}}</title>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1">
   <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
   <link href="styles/styles.css" rel="stylesheet">
 
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>   

  <link href="https://fonts.googleapis.com/css2?family=EB+Garamond&family=IM+Fell+English+SC&display=swap" rel="stylesheet">
   
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
      var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl)
      })
    });
  </script>

 </head>
 <body>
  <div class="container mt-4 mb-4">
    <div class="row">
      <div class="col-12">
        <div onclick="location.href='index.html';" class="rounded header-container hover-effect">
          <section>
            <header class="text-center">
              <div class="d-flex justify-content-center">
                <h1 class="page-title">Barbado's Tolkien Page v2.0</h1>
              </div>
            </header>
          </section>
        </div>
        <div class="entry-container">
          <div class="d-flex flex-column justify-content-center align-items-center">
            <div class="p-4">
              <div style="text-align: right;">
                <h1 class="entry-title">{{main_title}}</h1>
                <h2 class="entry-subtitle">{{additional_titles}}</h2>
                <hr />
                <span class="tag {{category_short_description}}">{{category_description}}</span>
              </div>
              {{image}} 
              {{content}}
            </div>        
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    $('.entry-image').on('click', function(){
      src = $(this).attr('src');
      $('.overlay img').attr('src', src);
      $('.overlay').show();
      $('.entry-text').hide();
    });

    $('.overlay').on('click', function(){
      $('.overlay').hide();
      $('.entry-text').show();
    });
  </script>              

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
 </body>
</html>