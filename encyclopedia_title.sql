INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (1, 1, 'Amroth', 'amroth', 1);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (2, 1, '<PERSON><PERSON><PERSON>', 'anarion', 2);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (3, 1, 'Ancalagon', 'ancalagon', 3);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (4, 1, 'Aragorn I', 'aragorn_i', 4);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (5, 1, 'Aragorn II', 'aragorn_ii', 5);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (6, 0, 'Thengel', 'aragorn_ii', 5);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (7, 0, 'Ecthelion', 'aragorn_ii', 5);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (8, 0, 'Thorongil', 'aragorn_ii', 5);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (9, 0, 'Pedra-élfica', 'aragorn_ii', 5);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (10, 0, 'Elessar', 'aragorn_ii', 5);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (11, 0, 'Passolargo', 'aragorn_ii', 5);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (12, 0, 'Strider', 'aragorn_ii', 5);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (13, 1, 'Arien', 'arien', 6);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (14, 1, 'Arwen', 'arwen', 7);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (15, 0, 'Undómiel', 'arwen', 7);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (16, 1, 'Aulë', 'aule', 8);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (17, 0, 'Mahal', 'aule', 8);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (18, 1, 'Azaghâl', 'azaghal', 9);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (19, 1, 'Azog', 'azog', 10);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (20, 1, 'Balin', 'balin', 11);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (21, 1, 'Barbárvore', 'barbarvore', 12);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (22, 0, 'Treebeard', 'barbarvore', 12);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (23, 0, 'Fangorn', 'barbarvore', 12);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (24, 1, 'Bard', 'bard', 13);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (25, 1, 'Beorn', 'beorn', 14);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (26, 1, 'Beren', 'beren', 15);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (27, 1, 'Bifur', 'bifur', 16);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (28, 1, 'Bilbo Bolseiro', 'bilbo_bolseiro', 17);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (29, 0, 'Bilbo Baggins', 'bilbo_bolseiro', 17);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (30, 1, 'Bofur', 'bofur', 18);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (31, 1, 'Bolg', 'bolg', 19);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (32, 1, 'Bombur', 'bombur', 20);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (33, 1, 'Boromir', 'boromir', 21);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (34, 1, 'Carcharoth', 'carcharoth', 22);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (35, 1, 'Casca-de-pele', 'casca_de_pele', 23);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (36, 0, 'Skinbark', 'casca_de_pele', 23);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (37, 0, 'Fladrif', 'casca_de_pele', 23);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (38, 1, 'Celeborn', 'celeborn', 24);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (39, 1, 'Celebrian', 'celebrian', 25);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (40, 1, 'Celebrimbor', 'celebrimbor', 26);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (41, 1, 'Círdan', 'cirdan', 27);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (42, 1, 'Dáin I', 'dain_i', 28);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (43, 1, 'Dáin II', 'dain_ii', 29);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (44, 1, 'Denethor I', 'denethor_i', 30);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (45, 1, 'Denethor II', 'denethor_ii', 31);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (46, 1, 'Dior', 'dior', 32);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (47, 1, 'Dori', 'dori', 33);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (48, 1, 'Draugluin', 'draugluin', 34);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (49, 1, 'Durin I', 'durin_i', 35);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (50, 1, 'Durin II', 'durin_ii', 36);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (51, 1, 'Durin III', 'durin_iii', 37);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (52, 1, 'Durin VI', 'durin_vi', 38);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (53, 1, 'Durin VII', 'durin_vii', 39);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (54, 1, 'Dwalin', 'dwalin', 40);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (55, 1, 'Eärendil', 'earendil', 41);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (56, 1, 'Elendil', 'elendil', 42);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (57, 1, 'Elladan e Ellohir', 'elladan_e_ellohir', 43);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (58, 1, 'Elrond e Elros', 'elrond_e_elros', 44);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (59, 1, 'Elwë Singollo', 'elwe_singollo', 45);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (60, 1, 'Elwing', 'elwing', 46);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (61, 1, 'Éomer', 'eomer', 47);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (62, 1, 'Eonwë', 'eonwe', 48);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (63, 1, 'Eorl', 'eorl', 49);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (64, 1, 'Éowyn', 'eowyn', 50);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (65, 1, 'Estë', 'este', 51);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (66, 1, 'Faramir', 'faramir', 52);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (67, 1, 'Feänor', 'feanor', 53);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (68, 1, 'Felaróf', 'felarof', 54);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (69, 1, 'Fíli', 'fili', 55);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (70, 1, 'Fimbrethil', 'fimbrethil', 56);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (71, 0, 'Wandlimb', 'fimbrethil', 56);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (72, 0, 'Pé-de-fada', 'fimbrethil', 56);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (73, 1, 'Finarfin', 'finarfin', 57);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (74, 1, 'Finduilas', 'finduilas', 58);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (75, 1, 'Fingolfin', 'fingolfin', 59);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (76, 1, 'Fingon', 'fingon', 60);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (77, 1, 'Finrod Felagund', 'finrod_felagund', 61);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (78, 1, 'Finwë', 'finwe', 62);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (79, 1, 'Frodo Bolseiro', 'frodo_bolseiro', 63);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (80, 0, 'Frodo Baggins', 'frodo_bolseiro', 63);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (81, 1, 'Fruta D\'ouro', 'fruta_d_ouro', 64);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (82, 0, 'Goldberry', 'fruta_d_ouro', 64);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (83, 1, 'Galadriel', 'galadriel', 65);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (84, 1, 'Gandalf', 'gandalf', 66);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (85, 0, 'Mithrandir', 'gandalf', 66);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (86, 0, 'Tharkûn', 'gandalf', 66);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (87, 0, 'Incanûs', 'gandalf', 66);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (88, 0, 'Olórin', 'gandalf', 66);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (89, 1, 'Ghan-buri-ghan', 'ghan_buri_ghan', 67);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (90, 1, 'Gil-galad', 'gil_galad', 68);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (91, 1, 'Gimli', 'gimli', 69);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (92, 1, 'Glaurung', 'glaurung', 70);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (93, 1, 'Glóin', 'gloin', 71);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (94, 1, 'Glorfindel', 'glorfindel', 72);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (95, 1, 'Gollum', 'gollum', 73);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (96, 0, 'Sméagol', 'gollum', 73);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (97, 1, 'Gorbag', 'gorbag', 74);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (98, 1, 'Gothmog', 'gothmog', 75);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (99, 1, 'Grishnákh', 'grishnakh', 76);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (100, 1, 'Gwaihir', 'gwaihir', 77);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (101, 1, 'Helm-mão-de-martelo', 'helm_mao_de_martelo', 78);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (102, 0, 'Helm Hamerhand', 'helm_mao_de_martelo', 78);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (103, 1, 'Huan', 'huan', 79);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (104, 1, 'Húrin', 'hurin', 80);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (105, 1, 'Ilmarë', 'ilmare', 81);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (106, 1, 'Imrahil', 'imrahil', 82);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (107, 1, 'Ingwë', 'ingwe', 83);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (108, 1, 'Isildur', 'isildur', 84);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (109, 1, 'Khamûl', 'khamul', 85);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (110, 1, 'Kili', 'kili', 86);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (111, 1, 'Landroval', 'landroval', 87);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (112, 1, 'Legolas', 'legolas', 88);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (113, 1, 'Língua-de-cobra', 'lingua_de_cobra', 89);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (114, 0, 'Wormtongue', 'lingua_de_cobra', 89);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (115, 1, 'Lórien', 'lorien', 90);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (116, 1, 'Lúthien', 'luthien', 91);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (117, 1, 'Mandos', 'mandos', 92);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (118, 1, 'Manwë', 'manwe', 93);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (119, 1, 'Mecha De Folha', 'mecha_de_folha', 94);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (120, 0, 'Leaflock', 'mecha_de_folha', 94);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (121, 1, 'Melian', 'melian', 95);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (122, 1, 'Melkor', 'melkor', 96);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (123, 1, 'Meriadoc Brandebuque', 'meriadoc_brandebuque', 97);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (124, 0, 'Meriadoc Brandybuck', 'meriadoc_brandebuque', 97);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (125, 1, 'Mîm', 'mim', 98);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (126, 1, 'Morgoth', 'morgoth', 99);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (127, 1, 'Nahar', 'nahar', 100);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (128, 1, 'Nessa', 'nessa', 101);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (129, 1, 'Nienna', 'nienna', 102);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (130, 1, 'Nimrodel', 'nimrodel', 103);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (131, 1, 'Nori', 'nori', 104);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (132, 1, 'Óin', 'oin', 105);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (133, 1, 'Olwë', 'olwe', 106);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (134, 1, 'Ori', 'ori', 107);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (135, 1, 'Oromë', 'orome', 108);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (136, 1, 'Ossë', 'osse', 109);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (137, 1, 'Peregrin Tûk', 'peregrin_tuk', 110);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (138, 0, 'Peregrin Took', 'peregrin_tuk', 110);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (139, 1, 'Radagast', 'radagast', 111);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (140, 1, 'Radbug', 'radbug', 112);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (141, 1, 'Rei Dos Bruxos', 'rei_dos_bruxos', 113);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (142, 0, 'Witch-king', 'rei_dos_bruxos', 113);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (143, 1, 'Roäc', 'roac', 114);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (144, 1, 'Roheryn', 'roheryn', 115);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (145, 1, 'Samwise Gamgi', 'samwise_gamgi', 116);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (146, 0, 'Samwise Gamgee', 'samwise_gamgi', 116);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (147, 1, 'Saruman', 'saruman', 117);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (148, 0, 'Curumo', 'saruman', 117);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (149, 0, 'Curunír', 'saruman', 117);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (150, 1, 'Sauron', 'sauron', 118);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (151, 0, 'Annatar', 'sauron', 118);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (152, 0, 'Senhor Do Anel', 'sauron', 118);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (153, 0, 'Ring Lord', 'sauron', 118);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (154, 0, 'Necromante', 'sauron', 118);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (155, 0, 'Necromancer', 'sauron', 118);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (156, 1, 'Scadufax', 'scadufax', 119);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (157, 0, 'Shadowfax', 'scadufax', 119);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (158, 0, 'Facho-de-sombra', 'scadufax', 119);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (159, 1, 'Scatha', 'scatha', 120);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (160, 1, 'Shagrat', 'shagrat', 121);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (161, 1, 'Smaug', 'smaug', 122);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (162, 1, 'Snaga', 'snaga', 123);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (163, 1, 'Snawmana', 'snawmana', 124);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (164, 0, 'Snowmane', 'snawmana', 124);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (165, 1, 'Théoden', 'theoden', 125);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (166, 1, 'Thingol', 'thingol', 126);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (167, 1, 'Thorin I', 'thorin_i', 127);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (168, 1, 'Thorin II', 'thorin_ii', 128);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (169, 1, 'Thorin III', 'thorin_iii', 129);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (170, 1, 'Thorondor', 'thorondor', 130);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (171, 1, 'Thráin I', 'thrain_i', 131);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (172, 1, 'Thráin II', 'thrain_ii', 132);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (173, 1, 'Thuringwethil', 'thuringwethil', 133);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (174, 1, 'Tilion', 'tilion', 134);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (175, 1, 'Tom Bombadil', 'tom_bombadil', 135);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (176, 0, 'Iarwain Ben-adar', 'tom_bombadil', 135);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (177, 0, 'Forn', 'tom_bombadil', 135);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (178, 0, 'Orald', 'tom_bombadil', 135);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (179, 1, 'Tronquesperto', 'tronquesperto', 136);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (180, 0, 'Quickbeam', 'tronquesperto', 136);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (181, 0, 'Bregalad', 'tronquesperto', 136);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (182, 1, 'Tulkas', 'tulkas', 137);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (183, 1, 'Tuor', 'tuor', 138);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (184, 1, 'Turgon', 'turgon', 139);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (185, 1, 'Túrin', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (186, 0, 'Turambar', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (187, 0, 'Neithan', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (188, 0, 'Gorthol', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (189, 0, 'Helmo De Dragão', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (190, 0, 'Dragon-helm', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (191, 0, 'Mormegil', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (192, 0, 'Espada Negra', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (193, 0, 'Black Sword', 'turin', 140);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (194, 1, 'Ugluk', 'ugluk', 141);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (195, 1, 'Uinen', 'uinen', 142);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (196, 1, 'Ulmo', 'ulmo', 143);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (197, 1, 'Ungoliant', 'ungoliant', 144);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (198, 1, 'Vairë', 'vaire', 145);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (199, 1, 'Vána', 'vana', 146);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (200, 1, 'Varda', 'varda', 147);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (201, 0, 'Tintalë', 'varda', 147);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (202, 0, 'Elentári', 'varda', 147);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (203, 0, 'Fanuilos', 'varda', 147);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (204, 0, 'Snow-white', 'varda', 147);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (205, 0, 'Gilthoniel', 'varda', 147);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (206, 1, 'Yavanna', 'yavanna', 148);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (207, 1, 'Aglarond', 'aglarond', 149);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (208, 1, 'Almaren', 'almaren', 150);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (209, 1, 'Alqualondë', 'alqualonde', 151);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (210, 1, 'Aman', 'aman', 152);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (211, 1, 'Amon Amarth', 'amon_amarth', 153);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (212, 1, 'Amon Hen', 'amon_hen', 154);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (213, 1, 'Amon Lhaw', 'amon_lhaw', 155);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (214, 1, 'Amon Rûdh', 'amon_rudh', 156);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (215, 1, 'Amon Uilos', 'amon_uilos', 157);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (216, 1, 'Andor', 'andor', 158);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (217, 1, 'Andram', 'andram', 159);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (218, 1, 'Anduinë', 'anduine', 160);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (219, 1, 'Angband', 'angband', 161);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (220, 1, 'Angmar', 'angmar', 162);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (221, 1, 'Arda', 'arda', 163);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (222, 1, 'Argonath', 'argonath', 164);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (223, 1, 'Armenelos', 'armenelos', 165);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (224, 1, 'Arnor', 'arnor', 166);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (225, 1, 'Avallonë', 'avallone', 167);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (226, 1, 'Avathar', 'avathar', 168);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (227, 1, 'Azanulbizar', 'azanulbizar', 169);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (228, 1, 'Balar', 'balar', 170);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (229, 1, 'Barad Dûr', 'barad_dur', 171);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (230, 0, 'Lugbúrz', 'barad_dur', 171);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (231, 0, 'Torre Escura', 'barad_dur', 171);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (232, 0, 'Dark Tower', 'barad_dur', 171);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (233, 0, 'Grande Torre', 'barad_dur', 171);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (234, 0, 'Great Tower', 'barad_dur', 171);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (235, 1, 'Belegaer', 'belegaer', 172);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (236, 1, 'Belegost', 'belegost', 173);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (237, 1, 'Beleriand', 'beleriand', 174);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (238, 1, 'Bolsão', 'bolsao', 175);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (239, 0, 'Bag End', 'bolsao', 175);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (240, 1, 'Brejo Dos Rostos Mortos', 'brejo_dos_rostos_mortos', 176);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (241, 0, 'Mere Of Dead Faces', 'brejo_dos_rostos_mortos', 176);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (242, 1, 'Bri', 'bri', 177);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (243, 0, 'Bree', 'bri', 177);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (244, 1, 'Cair Andros', 'cair_andros', 178);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (245, 1, 'Calacirya', 'calacirya', 179);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (246, 1, 'Campos De Lis', 'campos_de_lis', 180);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (247, 0, 'Gladden Fields', 'campos_de_lis', 180);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (248, 1, 'Campos De Pelennor', 'campos_de_pelennor', 181);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (249, 0, 'Pelennor Fields', 'campos_de_pelennor', 181);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (250, 1, 'Caras Galadon', 'caras_galadon', 182);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (251, 1, 'Cataratas De Rauros', 'cataratas_de_rauros', 183);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (252, 0, 'Rauros Falls', 'cataratas_de_rauros', 183);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (253, 1, 'Celebrant', 'celebrant', 184);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (254, 0, 'Kibil-nâla', 'celebrant', 184);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (255, 0, 'Filão De Prata', 'celebrant', 184);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (256, 0, 'Silverlode', 'celebrant', 184);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (257, 1, 'Cerin Amroth', 'cerin_amroth', 185);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (258, 1, 'Cirith Gorgor', 'cirith_gorgor', 186);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (259, 1, 'Cirith Ungol', 'cirith_ungol', 187);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (260, 1, 'Colinas De Ferro', 'colinas_de_ferro', 188);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (261, 0, 'Iron Hills', 'colinas_de_ferro', 188);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (262, 1, 'Colinas Dos Túmulos', 'colinas_dos_tumulos', 189);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (263, 0, 'Barrow Downs', 'colinas_dos_tumulos', 189);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (264, 1, 'Colinas Do Vento', 'colinas_do_vento', 190);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (265, 0, 'Weather Hills', 'colinas_do_vento', 190);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (266, 1, 'Colina Do Corvo', 'colina_do_corvo', 191);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (267, 0, 'Ravenhil', 'colina_do_corvo', 191);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (268, 1, 'Condado', 'condado', 192);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (269, 0, 'Shire', 'condado', 192);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (270, 1, 'Dagorlad', 'dagorlad', 193);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (271, 1, 'Desfiladeiro De Helm', 'desfiladeiro_de_helm', 194);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (272, 0, 'Helm\'s Deep', 'desfiladeiro_de_helm', 194);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (273, 1, 'Dol Amroth', 'dol_amroth', 195);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (274, 1, 'Dol Guldur', 'dol_guldur', 196);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (275, 0, 'Torre Da Bruxaria', 'dol_guldur', 196);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (276, 0, 'Sorcery Tower', 'dol_guldur', 196);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (277, 1, 'Doriath', 'doriath', 197);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (278, 1, 'Dunharrow', 'dunharrow', 198);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (279, 1, 'Eä', 'ea', 199);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (280, 1, 'Edoras', 'edoras', 200);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (281, 1, 'Ekkaia', 'ekkaia', 201);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (282, 1, 'Eldamar', 'eldamar', 202);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (283, 1, 'Erebor', 'erebor', 203);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (284, 0, 'A Montanha Solitária', 'erebor', 203);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (285, 0, 'The Lonely Mountain', 'erebor', 203);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (286, 1, 'Eregion', 'eregion', 204);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (287, 1, 'Eriador', 'eriador', 205);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (288, 1, 'Esgaroth', 'esgaroth', 206);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (289, 1, 'Falas', 'falas', 207);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (290, 1, 'Fangorn', 'fangorn', 208);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (291, 0, 'Barbárvore', 'fangorn', 208);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (292, 0, 'Treebeard', 'fangorn', 208);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (293, 1, 'Floresta Druadan', 'floresta_druadan', 209);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (294, 0, 'Druadan Forest', 'floresta_druadan', 209);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (295, 1, 'Floresta Dos Ents', 'floresta_dos_ents', 210);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (296, 0, 'Entwood', 'floresta_dos_ents', 210);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (297, 0, 'Floresta De Fangorn', 'floresta_dos_ents', 210);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (298, 0, 'Fangorn Forest', 'floresta_dos_ents', 210);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (299, 1, 'Floresta Tenebrosa', 'floresta_tenebrosa', 211);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (300, 0, 'Mirkwood', 'floresta_tenebrosa', 211);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (301, 1, 'Floresta Velha', 'floresta_velha', 212);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (302, 0, 'Old Forest', 'floresta_velha', 212);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (303, 1, 'Floresta Verde', 'floresta_verde', 213);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (304, 0, 'Greenwood', 'floresta_verde', 213);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (305, 0, 'Eryn Lasgalen', 'floresta_verde', 213);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (306, 1, 'Formenos', 'formenos', 214);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (307, 1, 'Fornost', 'fornost', 215);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (308, 0, 'Norbury', 'fornost', 215);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (309, 0, 'Fosso Dos Mortos', 'fornost', 215);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (310, 0, 'Deadmen\'s Dike', 'fornost', 215);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (311, 1, 'Forochel', 'forochel', 216);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (312, 1, 'Forte Da Trombeta', 'forte_da_trombeta', 217);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (313, 0, 'Hornburg', 'forte_da_trombeta', 217);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (314, 1, 'Gondolin', 'gondolin', 218);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (315, 1, 'Gondor', 'gondor', 219);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (316, 1, 'Gorgoroth', 'gorgoroth', 220);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (317, 1, 'Harad', 'harad', 221);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (318, 1, 'Helcar', 'helcar', 222);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (319, 1, 'Helcaraxë', 'helcaraxe', 223);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (320, 1, 'Ilhas Encantadas', 'ilhas_encantadas', 224);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (321, 0, 'Enchanted Isles', 'ilhas_encantadas', 224);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (322, 1, 'Lmarin', 'lmarin', 225);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (323, 1, 'Isengard', 'isengard', 226);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (324, 1, 'Janela Do Por-do-sol', 'janela_do_por_do_sol', 227);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (325, 0, 'Window Of The Sunset', 'janela_do_por_do_sol', 227);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (326, 0, 'Janela Sobre O Oeste', 'janela_do_por_do_sol', 227);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (327, 0, 'Window On The West', 'janela_do_por_do_sol', 227);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (328, 1, 'Khand', 'khand', 228);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (329, 1, 'Khazad-dûm', 'khazad_dum', 229);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (330, 0, 'Moria', 'khazad_dum', 229);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (331, 1, 'Lindon', 'lindon', 230);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (332, 1, 'Lórellin', 'lorellin', 231);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (333, 1, 'Lórien', 'lorien', 232);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (334, 1, 'Lothlórien', 'lothlorien', 233);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (335, 1, 'Mandos', 'mandos', 234);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (336, 1, 'Matas Dos Trolls', 'matas_dos_trolls', 235);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (337, 0, 'Trollshaws', 'matas_dos_trolls', 235);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (338, 1, 'Meduseld', 'meduseld', 236);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (339, 1, 'Menegroth', 'menegroth', 237);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (340, 1, 'Meneltarma', 'meneltarma', 238);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (341, 1, 'Minas Anor', 'minas_anor', 239);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (342, 1, 'Minas Ithil', 'minas_ithil', 240);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (343, 1, 'Minas Morgul', 'minas_morgul', 241);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (344, 1, 'Minas Tirith', 'minas_tirith', 242);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (345, 1, 'Montanhas Azuis', 'montanhas_azuis', 243);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (346, 0, 'Ered Luin', 'montanhas_azuis', 243);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (347, 0, 'Blue Mountains', 'montanhas_azuis', 243);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (348, 1, 'Montanhas Brancas', 'montanhas_brancas', 244);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (349, 0, 'White Mountains', 'montanhas_brancas', 244);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (350, 0, 'Ered Nimrais', 'montanhas_brancas', 244);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (351, 1, 'Montanhas Das Cinzas', 'montanhas_das_cinzas', 245);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (352, 0, 'Ash Mountains', 'montanhas_das_cinzas', 245);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (353, 1, 'Montanhas Cinzentas', 'montanhas_cinzentas', 246);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (354, 0, 'Grey Mountains', 'montanhas_cinzentas', 246);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (355, 0, 'Ered Mithrin', 'montanhas_cinzentas', 246);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (356, 1, 'Montanhas Ecoantes', 'montanhas_ecoantes', 247);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (357, 0, 'Echoing Mountains', 'montanhas_ecoantes', 247);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (358, 0, 'Ered Lómin', 'montanhas_ecoantes', 247);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (359, 1, 'Montanhas De Ferro', 'montanhas_de_ferro', 248);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (360, 0, 'Iron Mountains', 'montanhas_de_ferro', 248);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (361, 1, 'Montanhas Nebulosas', 'montanhas_nebulosas', 249);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (362, 0, 'Misty Mountains', 'montanhas_nebulosas', 249);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (363, 1, 'Montanhas Pelóri', 'montanhas_pelori', 250);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (364, 0, 'Pelóri Mountains', 'montanhas_pelori', 250);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (365, 1, 'Montanhas Sombrias', 'montanhas_sombrias', 251);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (366, 0, 'Shadowy Mountains', 'montanhas_sombrias', 251);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (367, 1, 'Montanha Da Perdição', 'montanha_da_perdicao', 252);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (368, 0, 'Mount Doom', 'montanha_da_perdicao', 252);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (369, 1, 'Mordor', 'mordor', 253);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (370, 1, 'Moria', 'moria', 254);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (371, 1, 'Nargothrond', 'nargothrond', 255);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (372, 1, 'Nogrod', 'nogrod', 256);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (373, 1, 'Númenor', 'numenor', 257);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (374, 1, 'Nurn', 'nurn', 258);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (375, 1, 'Orocarni', 'orocarni', 259);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (376, 1, 'Orodruin', 'orodruin', 260);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (377, 1, 'Orthanc', 'orthanc', 261);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (378, 1, 'Osgiliath', 'osgiliath', 262);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (379, 1, 'Ossiriand', 'ossiriand', 263);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (380, 1, 'Ost-in-edhil', 'ost_in_edhil', 264);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (381, 1, 'Pântanos Mortos', 'pantanos_mortos', 265);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (382, 0, 'Dead Marshes', 'pantanos_mortos', 265);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (383, 1, 'Pelargir', 'pelargir', 266);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (384, 1, 'Portos Cinzentos', 'portos_cinzentos', 267);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (385, 0, 'Grey Havens', 'portos_cinzentos', 267);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (386, 1, 'Rhovanion', 'rhovanion', 268);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (387, 1, 'Rhûn', 'rhun', 269);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (388, 1, 'Rio Anduin', 'rio_anduin', 270);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (389, 0, 'Anduin River', 'rio_anduin', 270);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (390, 1, 'Rio Brandevin', 'rio_brandevin', 271);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (391, 0, 'Baranduin', 'rio_brandevin', 271);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (392, 0, 'Brandywine River', 'rio_brandevin', 271);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (393, 1, 'Rio Gelion', 'rio_gelion', 272);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (394, 0, 'Gelion River', 'rio_gelion', 272);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (395, 1, 'Rio Sirion', 'rio_sirion', 273);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (396, 0, 'Sirion River', 'rio_sirion', 273);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (397, 1, 'Rohan', 'rohan', 274);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (398, 1, 'Taniquetil', 'taniquetil', 275);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (399, 1, 'Terras Imortais', 'terras_imortais', 276);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (400, 0, 'Undying Lands', 'terras_imortais', 276);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (401, 1, 'Terra Média', 'terra_media', 277);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (402, 0, 'Middle Earth', 'terra_media', 277);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (403, 1, 'Thangorodrim', 'thangorodrim', 278);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (404, 1, 'Tirion', 'tirion', 279);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (405, 1, 'Tol Eressëa', 'tol_eressea', 280);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (406, 1, 'Tol Sirion', 'tol_sirion', 281);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (407, 1, 'Torre Branca', 'torre_branca', 282);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (408, 0, 'White Tower', 'torre_branca', 282);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (409, 1, 'Umbar', 'umbar', 283);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (410, 1, 'Utumno', 'utumno', 284);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (411, 1, 'Valfenda', 'valfenda', 285);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (412, 0, 'Rivendell', 'valfenda', 285);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (413, 1, 'Valimar', 'valimar', 286);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (414, 1, 'Valinor', 'valinor', 287);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (415, 1, 'Valle', 'valle', 288);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (416, 0, 'Dale', 'valle', 288);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (417, 1, 'Vila Dos Hobbits', 'vila_dos_hobbits', 289);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (418, 0, 'Hobbiton', 'vila_dos_hobbits', 289);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (419, 1, 'Voltavime', 'voltavime', 290);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (420, 0, 'Withywindle', 'voltavime', 290);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (421, 1, 'Zirak-zigil', 'zirak_zigil', 291);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (422, 1, 'A Criação De Arda', 'a_criacao_de_arda', 292);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (423, 1, 'As Eras Das Árvores', 'as_eras_das_arvores', 293);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (424, 1, 'As Eras Da Escuridão', 'as_eras_da_escuridao', 294);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (425, 1, 'Eras Das Estrelas', 'eras_das_estrelas', 295);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (426, 1, 'As Eras Das Lâmpadas', 'as_eras_das_lampadas', 296);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (427, 1, 'A Primeira Era Do Sol', 'a_primeira_era_do_sol', 297);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (428, 1, 'A Segunda Era Do Sol', 'a_segunda_era_do_sol', 298);
INSERT INTO encyclopedia.title (id, flag_primary, description, entry_tag, entry_id) VALUES (429, 1, 'A Terceira Era Do Sol', 'a_terceira_era_do_sol', 299);
