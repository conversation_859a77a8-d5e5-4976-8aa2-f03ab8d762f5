import mysql.connector
import json
from unidecode import unidecode
from profiler import Profiler

profiler = Profiler()
profiler.start("load_entries")

try:
   cnx = mysql.connector.connect(user='app_user', password='1234',host='localhost',database='encyclopedia')
except mysql.connector.Error as error:
   print(error)
   exit(1)
else:
   cursor = cnx.cursor(dictionary=True)

   select_category = "select id,short_description,description from category;"
   cursor.execute(select_category)
   categories = cursor.fetchall()

   categories_map = {}
   for category in categories:
      categories_map.__setitem__(category['short_description'],category)

   with open('entries.json') as json_file:
      file_data = json.load(json_file)
      json_file.close()

      insert_entry = "INSERT INTO entry (category_id, primary_title, title_tag, content, tagged_content) VALUES (%s, %s, %s, %s, %s);"
      insert_title = "INSERT INTO title (flag_primary, flag_hidden, description, entry_tag, entry_id) VALUES (%s, %s, %s, %s, %s);"

      for entry in file_data:
         profiler.start("process_entry")

         entry_tag    = entry['title'][0]
         entry_tag    = unidecode(entry_tag.lower().replace(" ", "_").replace("-", "_").replace("'", "_"))
         entry_values = (categories_map[entry['category']]['id'], entry['title'][0], entry_tag, entry['content'], "")
         cursor.execute(insert_entry,entry_values)

         flag_primary = 1
         entry_id     = cursor.lastrowid
         for title in entry['title']:
            profiler.start("process_title")
            title_values = (flag_primary, 0, title, entry_tag, entry_id)
            cursor.execute(insert_title,title_values)
            flag_primary = 0
            profiler.stop("process_title")

         if 'hidden_title' in entry:
            for title in entry['hidden_title']:
               profiler.start("process_hiden_title")
               title_values = (flag_primary, 1, title, entry_tag, entry_id)
               cursor.execute(insert_title,title_values)
               profiler.stop("process_hiden_title")

         profiler.stop("process_entry")

   cnx.commit()
   cnx.close()

profiler.stop("load_entries")
profiler.report()
