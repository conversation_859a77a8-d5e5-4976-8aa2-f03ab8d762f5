import mysql.connector
import json
from profiler import Profiler

profiler = Profiler()
profiler.start("load_categories")

try:
   cnx = mysql.connector.connect(user='app_user', password='1234',host='localhost',database='encyclopedia')
except mysql.connector.Error as error:
   print(error)
   exit(1)
else:
   cursor = cnx.cursor()

   with open('categories.json') as json_file:
      categories = json.load(json_file)
      json_file.close()

      insert_categories = "INSERT INTO category (short_description, description) VALUES (%s, %s);"

      for category in categories:
         profiler.start("insert_category")
         category_values = (category['short_description'], category['description'])
         cursor.execute(insert_categories,category_values)
         profiler.stop("insert_category")

   cnx.commit()
   cnx.close()

profiler.stop("load_categories")
profiler.report()
