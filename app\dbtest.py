# CREATE USER 'teste'@'localhost' IDENTIFIED BY '1234';
# CREATE USER 'teste'@'%' IDENTIFIED BY '1234';
# ALTER USER 'root'@'localhost' IDENTIFIED BY '';
# CREATE DATABASE teste;
# grant all privileges on teste.* to 'teste'@'%';

# SELECT * FROM mysql.user;
# SHOW DATABASES;

import mysql.connector

try:
   cnx = mysql.connector.connect(user='teste', password='1234',host='localhost',database='')
   print("Test suceeded.")
except mysql.connector.Error as error:
   print("Test failed.")
   print(error)
else:
   cnx.close()
