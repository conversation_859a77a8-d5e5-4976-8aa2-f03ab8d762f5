import time

class Profiler:
   class ProfilerEntry:
      def __init__(self, name):
         self.name       = name
         self.count      = 0
         self.time       = 0
         self.running    = False
         self.start_time = 0

      def start(self):
         if (self.running):
            raise RuntimeWarning("Staring a profiler that is already running")
         
         self.start_time  = time.time()
         self.running    = True
         self.count     += 1

      def stop(self):
         if (not self.running):
            raise RuntimeWarning("Stoping a profiler that is not running")

         self.time    += time.time() - self.start_time
         self.running  = False

   def __init__(self):
      self.entries = {}

   def start(self, name):
      if name not in self.entries.keys():
         self.entries[name] = self.ProfilerEntry(name)

      entry = self.entries[name]
      entry.start()

   def stop(self, name):
      if name not in self.entries.keys():
         raise RuntimeWarning("Stoping a profiler that is not found")

      entry = self.entries[name]
      entry.stop()

   def report(self):
      print("-------------+--------------+-----------+---------+--------------------")
      print(" total time  |   avg time   |   count   | running |  name")
      print("-------------+--------------+-----------+---------+--------------------")
      for entry in self.entries:
         print("{:9,.2f}".format(1000 * self.entries[entry].time), "ms |", \
               "{:9,.2f}".format(1000 * self.entries[entry].time/self.entries[entry].count),"ms | ", \
               "{:7,.0f}".format(self.entries[entry].count)," | ", \
               self.entries[entry].running, " | ", \
               entry)
      print("-------------+--------------+--- -------+---------+--------------------")
         