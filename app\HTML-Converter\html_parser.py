from html.parser import HTMLParser
from json import JSONEncoder
import json
import os
import string

class MyHTMLParser(HTMLParser):
    __field    = ""
    title      = ""
    content    = ""

    def __init__(self, fileName, encoding = "latin_1"):
        super().__init__()

        # latin_1, iso-8859-1, iso8859-1, 8859, cp819, latin, latin1, L1
        # cp1252, windows-1252
        # utf_8, U8, UTF, utf8
        with open(fileName, mode="rt", encoding=encoding) as f:
            content = f.read()
            self.feed(content)
            f.close()

    def toJSON(self):
        return json.dumps(self.getEntry(), indent=4, ensure_ascii=False)

    def handle_starttag(self, tag, attrs):
        if(tag == "h1"):
            self.__field = "title"
        elif(tag == "image"):
            if "../alfabeto/" in attrs[0][1]:
                if(len(self.content) != 0):
                    self.content = self.content + "\n"
                self.content = self.content + attrs[0][1].replace("../alfabeto/","").replace(".gif","").upper()
                self.__field = "content"
        elif((self.__field == "content") & (tag != "br")):
            self.__field = ""

    def handle_data(self, data):
        if(self.__field == "title"):
            # self.title = data.replace('\n', ' ').capitalize()
            self.title = string.capwords(data.replace('\n', ' ').replace(' (', ', ').replace(')',''))
            self.title = self.title.split(', ')
            self.__field = ""
        elif(self.__field == "content"):
            self.content = self.content + data.rstrip().replace('\n', ' ').replace('\r', '')

    def getEntry(self):
        return {'title': self.title, 'content': self.content}

base_dir = "./data/"
site_content = []

for d in os.listdir(base_dir):
    for f in os.listdir(base_dir + d + "/"):
        entry = MyHTMLParser(base_dir + d + "/"+ f).getEntry()
        entry["category"] = d
        site_content.append(entry)

# print(json.dumps(site_content, indent=4, ensure_ascii=False))

# latin_1, iso-8859-1, iso8859-1, 8859, cp819, latin, latin1, L1
# cp1252, windows-1252
# utf_8, U8, UTF, utf8

f = open("site_content.json","wt", encoding = "utf8")
f.write(json.dumps(site_content, indent=4, ensure_ascii=False))
f.close()