import mysql.connector
from profiler import Profiler

profiler = Profiler()
profiler.start("parse_tags")

def tag_text(text, tag):
   text = " " + text # if text starts with tag, it will not be detected

   tagged = ""
   start = 0
   while start < len(text):
      end = text[start:len(text)].find("{{")
      if end == -1:
         end = len(text)
      else:
         end += start

      tagged += text[start:end].replace(" " + tag + " ", " {{" + tag + "}} ") \
                               .replace(" " + tag + ")", " {{" + tag + "}})") \
                               .replace("(" + tag + ")", "({{" + tag + "}})") \
                               .replace("(" + tag + ",", "({{" + tag + "}},") \
                               .replace(" " + tag + ",", " {{" + tag + "}},") \
                               .replace(" " + tag + ".", " {{" + tag + "}}.") \
                               .replace(" " + tag + "!", " {{" + tag + "}}!") \
                               .replace(" " + tag + "?", " {{" + tag + "}}?") \
                               .replace(" " + tag + ";", " {{" + tag + "}};") \
                               .replace(" " + tag + ":", " {{" + tag + "}}:") \
                               .replace("\"" + tag + "\"", "\"{{" + tag + "}}\"") \
                               .replace("'" + tag + "'", "'{{" + tag + "}}'") \
                               .replace("\n" + tag + " ", "\n{{" + tag + "}} ") \
                               .replace("\n" + tag + ",", "\n{{" + tag + "}},")
      
      esc = text[start:len(text)].find("}}")
      if esc == -1:
         esc = len(text)
      else:
         esc += (start + 1)

      tagged += text[end:esc]

      start = esc

   return tagged.lstrip()


try:
   cnx = mysql.connector.connect(user='app_user', password='1234',host='localhost',database='encyclopedia')
except mysql.connector.Error as error:
   print(error)
   exit(1)
else:
   cursor = cnx.cursor(dictionary=True)

   select_entries = "select id, content, title_tag from entry;"
   cursor.execute(select_entries)
   entries = cursor.fetchall()

   select_titles = "select id, flag_primary, description, entry_tag, entry_id from title order by char_length(description) DESC,description;"
   cursor.execute(select_titles)
   titles = cursor.fetchall()

   update_entry = "update entry set tagged_content = %s where id = %s;"

   for title in titles:
      profiler.start("title_loop")
      for entry in entries:
         profiler.start("tags_loop")
         if (title['entry_tag'] != entry['title_tag']): ## if tag != current tag - do not link to the same page
            entry['content'] = tag_text(entry['content'], title['description'])
         profiler.stop("tags_loop")
      profiler.stop("title_loop")

   for entry in entries:
      profiler.start("updates")
      values = (entry['content'], entry['id'])
      cursor.execute(update_entry,values)
      profiler.stop("updates")

   cnx.commit()
   cnx.close()
  
profiler.stop("parse_tags")
profiler.report()
