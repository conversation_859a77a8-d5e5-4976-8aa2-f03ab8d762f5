/* Global styles */
body {
    background: #efe5d2 url('celtic2.jpg') repeat;
    font-family: 'E<PERSON> Garamond', serif;
}

/* Container */
.container {
    max-width: 90% !important;
    width: 90% !important;
}

/* Card styles */
.card {
    cursor: pointer;
    background: #f5f1e8 url('parchment.webp');
    background-size: cover;    
    border-radius: 0.375rem;
    border: 1px solid #d3b17d;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.card.bg00 {
    background: #f5f1e8 url('mini_parchment_00.jpg');
    background-size: cover;    
}

.card.bg01 {
    background: #f5f1e8 url('mini_parchment_01.jpg');
    background-size: cover;    
}

.card.bg02 {
    background: #f5f1e8 url('mini_parchment_02.jpg');
    background-size: cover;    
}

.card.bg03 {
    background: #f5f1e8 url('mini_parchment_03.jpg');
    background-size: cover;    
}

.card.bg04 {
    background: #f5f1e8 url('mini_parchment_04.jpg');
    background-size: cover;    
}

.card.bg05 {
    background: #f5f1e8 url('mini_parchment_05.jpg');
    background-size: cover;    
}

.card.bg06 {
    background: #f5f1e8 url('mini_parchment_06.jpg');
    background-size: cover;    
}

.card.bg07 {
    background: #f5f1e8 url('mini_parchment_07.jpg');
    background-size: cover;    
}

.card.bg08 {
    background: #f5f1e8 url('mini_parchment_08.jpg');
    background-size: cover;    
}

.card.bg09 {
    background: #f5f1e8 url('mini_parchment_09.jpg');
    background-size: cover;    
}

.card.transition {
    transition: all 0.2s ease-in-out;
}

.card.transition:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
}

/* Hover effects */
.hover-effect {
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
}

.hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
}

/* Card body */
.card-body {
    padding: 0.5rem 1rem;  /* py-2 px-3 */
    display: flex;
    flex-direction: column;
    height: 100%; /* Important to make it fill the card */    
}

/* Typography - Card text */
.card-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #341502;
}

.card-subtitle {
    font-size: 1.1rem;
    color: #3f2514;
    margin-bottom: 0.25rem;
}

.text-end {
  text-align: right !important;
  margin-top: auto; /* Push to the bottom */
}

.tag {
    font-size: 1.1rem;
    letter-spacing: 0.025em;
    padding: 0.25rem 0.5rem;
    font-weight: 700;
    margin-bottom: 0;
    color: #000000;
    border-radius: 0.375rem;
}

.tag.histo {
    color: #045a42d0;
    border: 1px solid #045a4240;
    background-color: #045a420e;
}

.tag.geogr {
    color: #6e1931d0;
    border: 1px solid #6e193140;
    background-color: #6e19310e;
}

.tag.biogr {
    color: #19296ed0;
    border: 1px solid #2b4c7e40;
    background-color: #2b4c7e0e;
}

.tag.socio {
    color: #472b7ed0;
    border: 1px solid #4e2b7e40;
    background-color: #4b2b7e0e;
}

.tag.fauna {
    color: #c2410cd0;
    border: 1px solid #c2410c40;
    background-color: #c2410c0e;
}

.tag.flora {
    color: #713f12d0;
    border: 1px solid #713f1240;
    background-color: #713f120e;
}

.tag.artef {
    color: #44403cd0;
    border: 1px solid #44403c40;
    background-color: #44403c0e;
}

/* Layout utilities */
.card-container {
    margin-bottom: 1rem;
    transition: opacity 0.15s ease-out;
    display: block;
}

.card-container.hidden {
    opacity: 0;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute !important;
    pointer-events: none;
}

/* Masonry grid */
.masonry-grid {
    display: flex;
    flex-wrap: wrap;
    margin-left: -8px;
    margin-right: -8px;
}

.masonry-grid > .card-container {
    padding: 0 8px;
    margin-bottom: 16px;
    display: flex;
    width: 25%;
}

@media (max-width: 1400px) {
    .masonry-grid > .card-container {
        width: 33.333%;
    }
}

@media (max-width: 992px) {
    .masonry-grid > .card-container {
        width: 50%;
    }
}

@media (max-width: 576px) {
    .masonry-grid > .card-container {
        width: 100%;
    }
}

.masonry-grid > .card-container > .card {
    width: 100%;
}

.category-toggle {
    display: inline-flex;
    align-items: center;
    margin: 0.5rem;
    border-radius: 0.375rem;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    font-size: 1.1rem;
    letter-spacing: 0.025em;
    padding: 0.25rem 0.5rem;
    font-weight: 700;
    margin-bottom: 0;
    color: #000000;
}

.category-toggle.socio {
    color: #472b7ed0;
    border: 1px solid #4e2b7e40;
    background-color: #4b2b7e25;
}

.category-toggle.histo {
    color: #045a42d0;
    border: 1px solid #045a4240;
    background-color: #045a4225;
}

.category-toggle.geogr {
    color: #6e1931d0; 
    border: 1px solid #6e193140;
    background-color: #6e193125;
}

.category-toggle.biogr {
    color: #19296ed0;
    border: 1px solid #2b4c7e40;
    background-color: #2b4c7e25;
}

.category-toggle.fauna {
    color: #c2410cd0;
    border: 1px solid #c2410c40;
    background-color: #c2410c25;
}

.category-toggle.flora {
    color: #713f12d0;
    border: 1px solid #713f1240;
    background-color: #713f1225;
}

.category-toggle.artef {
    color: #44403cd0;
    border: 1px solid #44403c40;
    background-color: #44403c25;
}

.form-check-input[type=checkbox] {
    /* TODO: LEARN WHAT IS THIS */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff' stroke='%23ffffff' stroke-width='1'/%3e%3c/svg%3e") !important;
    border: 1px solid #d1d5db !important;
    background-color: #a2a3a5 !important;
    position: relative !important;
    background-repeat: no-repeat !important;
}

.form-check-input[type=checkbox]:checked.biogr {
    background-color: #1e40af !important; /* Azul Escuro */
    border-color: #1e40af !important;
}

.form-check-input[type=checkbox]:checked.geogr {
    background-color: #9f1239 !important; /* Vinho / Bordô */
    border-color: #9f1239 !important;
}

.form-check-input[type=checkbox]:checked.histo {
    background-color: #065f46 !important; /* Verde Floresta */
    border-color: #065f46 !important;
}

.form-check-input[type=checkbox]:checked.socio {
    background-color: #19065f !important; /* Roxo / Índigo Escuro */
    border-color: #28065f !important;
}

.form-check-input[type=checkbox]:checked.fauna {
    background-color: #c2410c !important; /* Laranja Queimado */
    border-color: #c2410c !important;
}

.form-check-input[type=checkbox]:checked.flora {
    background-color: #713f12 !important; /* Marrom Terra */
    border-color: #713f12 !important;
}

.form-check-input[type=checkbox]:checked.artef {
    background-color: #44403c !important; /* Cinza-chumbo */
    border-color: #44403c !important;
}

/* Typography */
.page-title {
    font-family: 'IM Fell English SC', serif;
    font-size: 3rem;
    font-weight: bold;
    color: #2e2b25;
    margin: 0;
    padding: 0.5rem 0;
}

.header-container {
    padding: 0.5rem !important;
    background: #b99d5b url('parchment.webp');
    background-size: cover;
}

.small {
    font-size: 0.875rem !important;
}

/* Entry page styles */
.entry-title {
    font-size: 3rem;
    font-weight: bold;
    color: #2e2b25;
    text-align: center;
    margin-bottom: 0rem;
}

.entry-subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    color: #423927;
    text-align: center;
    margin-top: 0;
}

/* Links */
a {
    color: #772803; /* #772803 */
    text-decoration: none;
    font-weight: bold;
    text-indent: 0px;
}

a:hover {
    color: #b1511d;
}

.entry-text {
    font-size: 1.2rem;
    text-align: justify;
    margin-top: 0.5rem;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    color: #1e1610;  /* #2c1f16 */
    font-weight: 500;
}

.entry-image {
    width: 250px;
    margin-right: 1rem;
    margin-top: 0.75rem;
    float: left; 
    cursor: pointer;    
    border: 4px solid #8a6a3d;
    box-shadow: 2px 2px 8px rgba(0,0,0,0.3);
    opacity: 0.8;
}

.entry-image.landscape{
    width: 450px;
    float: none; 
    display: block;
    margin: auto;
}

@media (min-width: 992px) {
    .entry-image {
        margin-right: 1.5rem;
        width: 300px;
        float: left; 
    }
    .entry-image.landscape{
        margin-right: 1.5rem;
        margin-top: 0.75rem;
        width: 400px;
        float: left; 
    }
}

@media (max-width: 724px) {
    .entry-image {
        margin-right: 0.75rem;
        width: 250px;
        float: none; 
        display: block;
        margin: auto;
    }
    .entry-image.landscape{
        width: 350px;
    }
}

@media (max-width: 576px) {
    .entry-image {
        width: 250px;
        float: none; 
        display: block;
        margin: auto;
    }
    .entry-image.landscape{
        width: 300px;
    }
}

.overlay {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  background: #efe5d2 url('celtic2.jpg') repeat;
  white-space: nowrap;

  height: 100%;
  width: 100%;

  text-align: center;
  cursor: pointer;
}

.overlay img {
  height: 90dvh;
  width: auto;
  border:5px solid #8a6a3d;
  vertical-align: middle;
}

.overlay.landscape img {
  width: 90dvw;
  height: auto;
}

.helper {
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

/* Form elements */
.form-check-input {
    border: none;
    width: 2.5em;
    height: 1.25em;
    margin-top: 0.125em;
}

.form-check-input:focus {
    box-shadow: none;
    border-color: transparent;
}

/* Utility classes */
.rounded-md {
    border-radius: 0.375rem;
}

/* Category card layout */
.category-card {
    flex: 0 0 auto;
    width: 50%;
    margin-bottom: 1rem;
}

@media (min-width: 992px) {
    .category-card {
        width: 33.333%;
    }
}

@media (max-width: 576px) {
    .category-card {
        width: 100%;
    }
}

.category-card-body {
    display: flex;
    flex-direction: column;
}

/* Entry container */
.entry-container {
    width: 75%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 3rem;
    background: #f4ecdb url('parchment.webp');
    background-size: cover;    
    border-radius: 0.375rem;
    border: 1px solid #d3b17d;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

@media (max-width: 576px) {
    .entry-container {
        width: 100%;
    }
}

.popover-wide {
    max-width: 400px !important;
}

p { 
   /* text-indent: 30px; */
}

hr {
  margin: 1rem auto 1rem auto;
  border: none;
  border-top: 2px solid #5e3d1e;
  width: 60%;
  opacity: 1;
}