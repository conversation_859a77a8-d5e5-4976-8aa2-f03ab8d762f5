<!DOCTYPE html>
<html lang="pt-br">
 <head>
   <title>Barbado's Tolkien Page v2.0</title>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1">
   <!-- Bootstrap CSS -->
   <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
   <link href="styles/styles.css" rel="stylesheet">
   <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>   
   <link href="https://fonts.googleapis.com/css2?family=EB+Garamond&family=IM+Fell+English+SC&display=swap" rel="stylesheet">
   <script>
      function filterCategory(category) {
         const categoryClass = category.id;
         const items = document.getElementsByClassName(categoryClass);
         
         Array.from(items).forEach(item => {
            if (category.checked) {
               item.classList.remove('hidden');
            } else {
               item.classList.add('hidden');
            }
         });

         // Force layout recalculation
         const grid = document.querySelector('.masonry-grid');
         grid.style.display = 'none';
         grid.offsetHeight; // Force reflow
         grid.style.display = 'flex';
      }

      document.addEventListener('DOMContentLoaded', function() {
         // No need for Masonry initialization - using CSS Grid/Flexbox instead
      });
   </script>
 </head>
 <body>
   <div class="container mt-4 mb-4">
      <div class="row">
         <div class="col-12">
            <div class="rounded header-container">
               <section>
                  <header class="text-center">
                     <div class="d-flex justify-content-center">
                        <h1 class="fw-semibold page-title">Barbado's Tolkien Page v2.0</h1>
                     </div>
                  </header>
               </section>
            </div>
         </div>
      </div>
   </div>

   <div class="container">
      <div class="row">
         <div class="col-12 d-flex justify-content-center flex-wrap">
{{categories}}
         </div>
      </div>
   </div>
   <div class="container mt-4">
      <div class="masonry-grid">
{{items}}
      </div>
   </div>
   <!-- Bootstrap Bundle with Popper -->
   <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
 </body>
</html>