# DEVELOPMENT DOCKERFILE
# docker build -t dev_env .
# docker run --rm -it -p 3306:3306 -v ${pwd}/app:/app dev_env

# sudo service mysql start
# /etc/init.d/mysql stop
# /usr/bin/mysqladmin -u root -p shutdown

# mysql
# use encyclopedia;
# show tables;
# select * from category;

# mysql < clean_db.sql
# python3 load_categories.py
# python3 load_entries.py
# python3 parse_tags.py
# python3 generate_htmls.py

# docker ps
# docker exec -it <container_name> bash

FROM ubuntu:22.04

# Set the working directory
WORKDIR /app

RUN mkdir -p /etc/mysql /app/mysql
COPY mysql_init_config.cnf /etc/mysql/my.cnf
COPY start.sh /usr/local/bin/start.sh

RUN apt-get update && apt-get install -y \
    mysql-server=8.0.28-0ubuntu4 \
    python3 \
    python3-mysql.connector \
    python3-unidecode \
    python3-pillow \
    sudo \
    vim \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && chown -R mysql:mysql /app/mysql \
    && chmod 700 /app/mysql \
    && usermod -d /app/mysql mysql \
    && chmod +x /usr/local/bin/start.sh

CMD ["bash", "/usr/local/bin/start.sh"]